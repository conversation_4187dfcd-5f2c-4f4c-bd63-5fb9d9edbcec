import pandas as pd
import numpy as np
import os
import glob
from tqdm import tqdm
from model import get_model
from metrics import calculate_performance
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args
import warnings
warnings.filterwarnings('ignore')

def filter_data_features(features):
    """
    Filter out signal generation features and return only actual data features
    """
    signal_features = ['C2C_LX', 'C2C_SX', 'Trail_LX', 'Trail_SX']
    return [f for f in features if f not in signal_features]

def analyze_required_parameters(features):
    """
    Analyze which parameters are needed based on the declared features
    """
    required_params = {}
    
    # Check if lag return features are used
    lag_features = [f for f in features if f.startswith('LagReturn')]
    if lag_features:
        # Extract the maximum lag number needed
        max_lag = max([int(f.replace('LagReturn', '')) for f in lag_features])
        required_params['n_lag'] = max_lag
    
    # Check if covariance features are used
    cov_features = [f for f in features if f.startswith('Cov')]
    if cov_features:
        required_params['cov_window'] = True
        # If we have lag returns, we already know n_lag, otherwise infer from cov features
        if 'n_lag' not in required_params:
            # Extract lag numbers from covariance features like 'Cov1_2', 'Cov2_2'
            lag_numbers = []
            for f in cov_features:
                parts = f.replace('Cov', '').split('_')
                lag_numbers.extend([int(p) for p in parts])
            required_params['n_lag'] = max(lag_numbers)
    
    # Check if SMA features are used
    sma_features = [f for f in features if f.startswith('SMA_') and not '_' in f[4:]]
    if sma_features:
        required_params['sma_periods'] = True
    
    # Check if SMA ratio features are used
    sma_ratio_features = [f for f in features if f.startswith('SMA_') and '_' in f[4:]]
    if sma_ratio_features:
        required_params['sma_periods'] = True  # Still need SMA periods for ratios
    
    # Check if EMA features are used
    ema_features = [f for f in features if f.startswith('EMA_')]
    if ema_features:
        required_params['ema_periods'] = True
    
    # Check if RSI is used
    if 'RSI' in features:
        required_params['rsi_window'] = True
    
    # Check if MACD features are used
    macd_features = [f for f in features if f.startswith('MACD')]
    if macd_features:
        required_params['macd_signal_span'] = True
        required_params['ema_periods'] = True  # MACD needs EMA
    
    # Check if Bollinger Band features are used
    bb_features = [f for f in features if f.startswith('BB_')]
    if bb_features:
        required_params['bb_params'] = True

    # Check if Aroon features are used
    aroon_features = [f for f in features if f.startswith('Aroon')]
    if aroon_features:
        required_params['aroon_period'] = True

    # Check if C2C signal generation features are used (but not Trail features)
    c2c_features = [f for f in features if f.startswith('C2C_')]
    if c2c_features:
        required_params['c2c_signals'] = True

    trail_features = [f for f in features if f.startswith('Trail_')]
    if trail_features:
        required_params['trail_signals'] = True

    return required_params

def create_optimization_space(required_params, minute_data=0):
    """
    Create optimization space based on required parameters
    """
    dimensions = []
    param_names = []
    
    # Add parameters based on what's actually needed
    if 'n_lag' in required_params:
        dimensions.append(Integer(3, min(10, required_params['n_lag'] + 2), name='n_lag'))
        param_names.append('n_lag')
    
    if 'cov_window' in required_params:
        dimensions.append(Integer(3, 10, name='cov_window'))
        param_names.append('cov_window')
    
    if 'sma_periods' in required_params:
        if minute_data != 0:
            # Intraday SMA periods
            dimensions.extend([
                Integer(10, 40, name='sma_1'),
                Integer(40, 100, name='sma_2'),
                Integer(100, 200, name='sma_3')
            ])
        else:
            # Daily SMA periods
            dimensions.extend([
                Integer(20, 60, name='sma_1'),
                Integer(80, 150, name='sma_2'),
                Integer(150, 300, name='sma_3')
            ])
        param_names.extend(['sma_1', 'sma_2', 'sma_3'])
    
    if 'ema_periods' in required_params:
        if minute_data != 0:
            # Intraday EMA periods
            dimensions.extend([
                Integer(2, 5, name='ema_1'),
                Integer(4, 10, name='ema_2')
            ])
        else:
            # Daily EMA periods
            dimensions.extend([
                Integer(30, 70, name='ema_1'),
                Integer(80, 150, name='ema_2')
            ])
        param_names.extend(['ema_1', 'ema_2'])
    
    if 'rsi_window' in required_params:
        dimensions.append(Integer(7, 28, name='rsi_window'))
        param_names.append('rsi_window')
    
    if 'macd_signal_span' in required_params:
        dimensions.append(Integer(6, 15, name='macd_signal_span'))
        param_names.append('macd_signal_span')
    
    if 'bb_params' in required_params:
        dimensions.extend([
            Integer(15, 30, name='bb_period'),
            Real(1.5, 3.0, name='bb_std_multiplier')
        ])
        param_names.extend(['bb_period', 'bb_std_multiplier'])

    if 'aroon_period' in required_params:
        dimensions.append(Integer(10, 30, name='aroon_period'))
        param_names.append('aroon_period')
    
    # Only include signal parameters if C2C features are used
    if 'c2c_signals' in required_params:
        dimensions.extend([
            Integer(2, 15, name='short_lookback_1'),
            Integer(2, 15, name='long_lookback_1')
        ])
        param_names.extend(['short_lookback_1', 'long_lookback_1'])
    
    # Only include Trail parameters if Trail features are used
    if 'trail_signals' in required_params:
        if minute_data != 0:
            # Intraday Trail periods
            dimensions.extend([
                Integer(2, 5, name='Trail_LX'),
                Integer(2, 5, name='Trail_SX')
            ])
        else:
            # Daily Trail periods
            dimensions.extend([
                Integer(5, 30, name='Trail_LX'),
                Integer(5, 30, name='Trail_SX')
            ])
        param_names.extend(['Trail_LX', 'Trail_SX'])

    return dimensions, param_names

def calculate_features_with_params(base_data, param_dict, features, minute_data=0):
    """
    Calculate only the features that are declared in the features list
    """
    data = base_data.copy()
    
    # Set default parameters
    default_params = {
        'sma_1': 50, 'sma_2': 100, 'sma_3': 200,
        'ema_1': 50, 'ema_2': 100,
        'rsi_window': 14, 'macd_signal_span': 9,
        'bb_period': 20, 'bb_std_multiplier': 2.0,
        'aroon_period': 14,
        'n_lag': 5, 'cov_window': 5,
        'short_lookback_1': 5, 'long_lookback_1': 10,
        'Trail_LX': 2, 'Trail_SX': 2
    }
    
    # Update with optimized parameters
    default_params.update(param_dict)
    
    # Only calculate features that are actually used
    feature_set = set(features)

    # Calculate SMA features if needed
    sma_features = [f for f in features if f.startswith('SMA_')]
    if sma_features:
        data['SMA_1'] = data['Close'].rolling(default_params['sma_1']).mean() / data['Close']
        data['SMA_2'] = data['Close'].rolling(default_params['sma_2']).mean() / data['Close']
        data['SMA_3'] = data['Close'].rolling(default_params['sma_3']).mean() / data['Close']
        
        # Calculate SMA ratios if needed
        if 'SMA_1_2' in feature_set:
            data['SMA_1_2'] = data['SMA_1'] / data['SMA_2']
        if 'SMA_1_3' in feature_set:
            data['SMA_1_3'] = data['SMA_1'] / data['SMA_3']
        if 'SMA_2_3' in feature_set:
            data['SMA_2_3'] = data['SMA_2'] / data['SMA_3']
    
    # Calculate RSI if needed
    if 'RSI' in feature_set:
        rsi_window = default_params['rsi_window']
        delta = data['Close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=rsi_window).mean()
        avg_loss = loss.rolling(window=rsi_window).mean()
        rs = avg_gain / avg_loss
        data['RSI'] = 1 - (1 / (1 + rs))
    
    # Calculate EMA and MACD if needed
    ema_or_macd_features = [f for f in features if f.startswith('EMA_') or f.startswith('MACD')]
    if ema_or_macd_features:
        data['EMA_1'] = data['Close'].ewm(span=default_params['ema_1'], adjust=False).mean()
        data['EMA_2'] = data['Close'].ewm(span=default_params['ema_2'], adjust=False).mean()
        
        # Calculate MACD features if needed
        macd_features = [f for f in features if f.startswith('MACD')]
        if macd_features:
            data['MACD'] = data['EMA_1'] - data['EMA_2']
            if 'MACD_Signal' in feature_set:
                data['MACD_Signal'] = data['MACD'].ewm(span=default_params['macd_signal_span'], adjust=False).mean()
            if 'MACD_Hist' in feature_set:
                if 'MACD_Signal' not in data.columns:
                    data['MACD_Signal'] = data['MACD'].ewm(span=default_params['macd_signal_span'], adjust=False).mean()
                data['MACD_Hist'] = data['MACD'] - data['MACD_Signal']
    
    # Calculate Bollinger Bands if needed
    bb_features = [f for f in features if f.startswith('BB_')]
    if bb_features:
        bb_period = default_params['bb_period']
        bb_multiplier = default_params['bb_std_multiplier']
        data['BB_Middle'] = data['Close'].rolling(bb_period).mean()
        data['BB_Std'] = data['Close'].rolling(bb_period).std()
        
        if 'BB_Upper' in feature_set:
            data['BB_Upper'] = data['BB_Middle'] + (data['BB_Std'] * bb_multiplier)
        if 'BB_Lower' in feature_set:
            data['BB_Lower'] = data['BB_Middle'] - (data['BB_Std'] * bb_multiplier)
        if 'BB_Width' in feature_set:
            if 'BB_Upper' not in data.columns:
                data['BB_Upper'] = data['BB_Middle'] + (data['BB_Std'] * bb_multiplier)
            if 'BB_Lower' not in data.columns:
                data['BB_Lower'] = data['BB_Middle'] - (data['BB_Std'] * bb_multiplier)
            data['BB_Width'] = (data['BB_Upper'] - data['BB_Lower']) / data['BB_Middle']

    # Calculate Aroon indicator if needed
    aroon_features = [f for f in features if f.startswith('Aroon')]
    if aroon_features:
        aroon_period = int(default_params['aroon_period'])

        # Calculate Aroon Up and Aroon Down
        if 'AroonUp' in feature_set:
            # Aroon Up = ((period - periods since highest high) / period) * 100
            highest_high_idx = data['High'].rolling(window=aroon_period).apply(lambda x: x.argmax(), raw=False)
            periods_since_high = aroon_period - 1 - highest_high_idx
            data['AroonUp'] = ((aroon_period - periods_since_high) / aroon_period) * 100

        if 'AroonDown' in feature_set:
            # Aroon Down = ((period - periods since lowest low) / period) * 100
            lowest_low_idx = data['Low'].rolling(window=aroon_period).apply(lambda x: x.argmin(), raw=False)
            periods_since_low = aroon_period - 1 - lowest_low_idx
            data['AroonDown'] = ((aroon_period - periods_since_low) / aroon_period) * 100

        if 'AroonOsc' in feature_set:
            # Aroon Oscillator = Aroon Up - Aroon Down
            if 'AroonUp' not in data.columns:
                highest_high_idx = data['High'].rolling(window=aroon_period).apply(lambda x: x.argmax(), raw=False)
                periods_since_high = aroon_period - 1 - highest_high_idx
                aroon_up = ((aroon_period - periods_since_high) / aroon_period) * 100
            else:
                aroon_up = data['AroonUp']

            if 'AroonDown' not in data.columns:
                lowest_low_idx = data['Low'].rolling(window=aroon_period).apply(lambda x: x.argmin(), raw=False)
                periods_since_low = aroon_period - 1 - lowest_low_idx
                aroon_down = ((aroon_period - periods_since_low) / aroon_period) * 100
            else:
                aroon_down = data['AroonDown']

            data['AroonOsc'] = aroon_up - aroon_down

    # Calculate lag returns if needed
    lag_features = [f for f in features if f.startswith('LagReturn')]
    if lag_features:
        n_lag = default_params['n_lag']
        # Extract the lag numbers from requested features
        requested_lags = []
        for feature in lag_features:
            if feature in feature_set:
                lag_num = int(feature.replace('LagReturn', ''))
                requested_lags.append(lag_num)

        # Create all requested lag return features
        for lag in requested_lags:
            data[f'LagReturn{lag}'] = data['Returns'].shift(lag)
    # Calculate covariance features if needed
    cov_features = [f for f in features if f.startswith('Cov')]
    if cov_features:
        cov_window = default_params['cov_window']
        n_lag = default_params['n_lag']

        # Calculate only the covariance features that are needed
        for cov_feature in cov_features:
            if cov_feature in feature_set:
                # Parse feature name like 'Cov1_2'
                parts = cov_feature.replace('Cov', '').split('_')
                lag1, lag2 = int(parts[0]), int(parts[1])

                # Ensure the required lag return features exist
                if f'LagReturn{lag1}' not in data.columns:
                    data[f'LagReturn{lag1}'] = data['Returns'].shift(lag1)
                if f'LagReturn{lag2}' not in data.columns:
                    data[f'LagReturn{lag2}'] = data['Returns'].shift(lag2)

                data[cov_feature] = data[f'LagReturn{lag1}'].rolling(cov_window).cov(data[f'LagReturn{lag2}'])
    
    # Create signals with optimizable parameters
    data['Signal'] = 0
    c2c_features = [f for f in features if f.startswith('C2C_')]
    if c2c_features:
        short_1 = min(2, default_params['short_lookback_1'])
        long_1 = min(2, default_params['long_lookback_1'])
        # Handle combined C2C_LX and C2C_SX
        if set(c2c_features) == {'C2C_LX', 'C2C_SX'}:
            data.loc[data['Close'].shift(-short_1) < data['Close'], 'Signal'] = -1
            data.loc[data['Close'].shift(-long_1) > data['Close'], 'Signal'] = 1
        # Handle individual features
        elif c2c_features == ['C2C_SX']:
            data.loc[data['Close'].shift(-short_1) < data['Close'], 'Signal'] = -1
        elif c2c_features == ['C2C_LX']:
            data.loc[data['Close'].shift(-long_1) > data['Close'], 'Signal'] = 1
    
    trail_features = [f for f in features if f.startswith('Trail_')]
    if trail_features:
        # Trail signals: Long when price breaks above trailing low, Short when price breaks below trailing high
        if 'Trail_LX' in trail_features:
            trailing_low = data['Low'].shift(1).rolling(window=default_params['Trail_LX']).min()
            data.loc[data['Close'] > trailing_low, 'Signal'] = 1
        if 'Trail_SX' in trail_features:
            trailing_high = data['High'].shift(1).rolling(window=default_params['Trail_SX']).max()
            data.loc[data['Close'] < trailing_high, 'Signal'] = -1
    
    data.loc[data.index.time > pd.to_datetime('15:30').time(), 'Signal'] = 0

    # Create target
    data['Target'] = data['Signal'].shift(-1)
    data.dropna(inplace=True)

    # Handle signal-generation features (these don't create actual columns)
    signal_features = ['C2C_LX', 'C2C_SX', 'Trail_LX', 'Trail_SX']

    # Separate actual data features from signal-generation features
    data_features = [f for f in features if f not in signal_features]
    signal_gen_features = [f for f in features if f in signal_features]

    # Check which data features actually exist in the DataFrame
    available_features = [f for f in data_features if f in data.columns]
    missing_features = set(data_features) - set(available_features)

    if missing_features:
        print(f"⚠️  Warning: Missing data features {missing_features}")
        print(f"   Available columns: {list(data.columns)}")

    # Return data with only the available features
    return data

def run_bayesian_optimization(file_path, minute_data=0, model_type='svm_vol',
                             train_months=12, test_months=1, features=None,
                             n_calls=50, random_state=42):
    """
    Run Bayesian optimization for hyperparameters of declared features only
    """
    print(f"Running Bayesian optimization for {model_type} model...")
    print(f"Declared features: {features}")

    # Analyze which parameters are actually needed
    required_params = analyze_required_parameters(features)
    print(f"Required parameters based on features: {list(required_params.keys())}")

    # Create optimization space for only the required parameters
    dimensions, param_names = create_optimization_space(required_params, minute_data)
    print(f"Optimizing {len(dimensions)} parameters: {param_names}")

    # Load base data
    base_data = pd.read_csv(file_path, parse_dates=True, index_col='Date')
    if minute_data != 0:
        base_data = base_data.resample(str(minute_data)+'min').agg({
            'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'
        })
        base_data.dropna(inplace=True)
    base_data.sort_index(inplace=True)
    base_data['Returns'] = base_data['Open'].diff()

    print(f"Base data loaded. Running Bayesian optimization with {n_calls} iterations...")

    # Initialize progress tracking
    iteration_count = 0
    best_score_so_far = float('inf')
    progress_bar = tqdm(total=n_calls, desc="Bayesian Optimization",
                       bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] Best: {postfix}')
    progress_bar.set_postfix_str("N/A")
    
    # Define objective function
    @use_named_args(dimensions)
    def objective(**params):
        nonlocal iteration_count, best_score_so_far, progress_bar

        try:
            # Ensure proper parameter ordering for SMA and EMA
            if 'sma_1' in params and 'sma_2' in params and 'sma_3' in params:
                if not (params['sma_1'] < params['sma_2'] < params['sma_3']):
                    iteration_count += 1
                    progress_bar.update(1)
                    return 1.0  # Return poor score for invalid ordering

            if 'ema_1' in params and 'ema_2' in params:
                if not (params['ema_1'] < params['ema_2']):
                    iteration_count += 1
                    progress_bar.update(1)
                    progress_bar.set_postfix_str(f"{-best_score_so_far:.4f}")
                    return 1.0  # Return poor score for invalid ordering

            # Calculate features with current parameters
            data = calculate_features_with_params(base_data, params, features, minute_data)
            
            # Walk-forward validation
            total_months = len(data.resample('ME').count())
            if total_months < train_months + test_months:
                iteration_count += 1
                progress_bar.update(1)
                progress_bar.set_postfix_str(f"{-best_score_so_far:.4f}")
                return 1.0

            period_results = []
            all_returns = pd.Series(index=data.index, dtype='float64')

            num_iterations = max(1, (total_months - train_months - test_months + 1) // test_months)

            for j in range(0, min(num_iterations * test_months, total_months - train_months - test_months + 1), test_months):
                try:
                    train_end = data.index[0] + pd.DateOffset(months=train_months + j)
                    test_end = train_end + pd.DateOffset(months=test_months)

                    train_data = data[(data.index >= data.index[0] + pd.DateOffset(months=j)) &
                                      (data.index < train_end)]
                    test_data = data[(data.index >= train_end) &
                                     (data.index < test_end)]

                    if len(train_data) == 0 or len(test_data) == 0:
                        continue

                    # Use only data features for model training (exclude signal generation features)
                    data_features = filter_data_features(features)
                    model = get_model(model_type, data_features)
                    model.train(train_data)
                    test_pred = model.predict(test_data)
                    test_returns = test_data['Returns'] * test_pred.shift(1).fillna(0)
                    all_returns[test_data.index] = test_returns.fillna(0)

                    performance = calculate_performance(test_data, test_pred, test_returns)
                    period_results.append(performance)

                except Exception as e:
                    continue

            if not period_results:
                iteration_count += 1
                progress_bar.update(1)
                progress_bar.set_postfix_str(f"{-best_score_so_far:.4f}")
                return 1.0

            # Return negative total return (since we want to minimize)
            total_return = all_returns.sum()
            score = -total_return

            # Update progress tracking
            iteration_count += 1
            if score < best_score_so_far:
                best_score_so_far = score

            progress_bar.update(1)
            progress_bar.set_postfix_str(f"{-best_score_so_far:.4f}")

            return score

        except Exception as e:
            iteration_count += 1
            progress_bar.update(1)
            progress_bar.set_postfix_str(f"{-best_score_so_far:.4f}")
            return 1.0
    
    # Run Bayesian optimization
    try:
        result = gp_minimize(objective, dimensions, n_calls=n_calls, random_state=random_state,
                            acq_func='EI', n_initial_points=10)
    finally:
        # Ensure progress bar is closed
        progress_bar.close()

    # Extract best parameters
    best_params = dict(zip(param_names, result.x))
    best_score = -result.fun  # Convert back to positive

    print(f"\nBayesian optimization completed!")
    print(f"Best total return: {best_score:.4f}")
    print(f"Best parameters: {best_params}")

    return {
        'best_params': best_params,
        'best_score': best_score,
        'optimization_result': result,
        'param_names': param_names,
        'required_params': required_params
    }

def load_optimized_parameters(features, optimization_results_file=None):
    """
    Load optimized parameters from Bayesian optimization results

    Parameters:
    -----------
    features : list
        List of features used in the strategy
    optimization_results_file : str, optional
        Path to specific optimization results file. If None, finds the most recent one.

    Returns:
    --------
    dict : Optimized parameters or None if no results found
    """


    try:
        # Find optimization results file
        if optimization_results_file is None:
            # Look for the single Bayesian optimization results file
            optimization_results_file = 'bayesian_optimization_results.csv'
            if not os.path.exists(optimization_results_file):
                print("❌ No Bayesian optimization results found!")
                print("   Run optimization first or specify the results file path.")
                return None
            print(f"📁 Loading parameters from: {optimization_results_file}")

        # Load the results
        results_df = pd.read_csv(optimization_results_file)

        if len(results_df) == 0:
            print("❌ Empty optimization results file!")
            return None

        # Filter results by matching features
        current_features_str = str(features)
        matching_results = results_df[results_df['features'] == current_features_str]

        if len(matching_results) == 0:
            print(f"❌ No optimization results found for features: {features}")
            print("   Available feature sets in results file:")
            for unique_features in results_df['features'].unique():
                print(f"     {unique_features}")
            return None

        # Get the most recent result for these features (sorted by timestamp)
        if 'timestamp' in matching_results.columns:
            best_result = matching_results.sort_values('timestamp', ascending=False).iloc[0]
            print(f"📅 Using most recent result from: {best_result['timestamp']}")
        else:
            best_result = matching_results.iloc[-1]  # Last entry if no timestamp

        # Extract parameters (exclude non-parameter columns)
        exclude_cols = ['timestamp', 'best_score', 'model_type', 'train_months', 'test_months', 'features', 'n_calls']
        param_cols = [col for col in results_df.columns if col not in exclude_cols]

        optimized_params = {}
        for col in param_cols:
            if pd.notna(best_result[col]):
                optimized_params[col] = best_result[col]

        # Features already verified by filtering above

        print(f"✅ Loaded optimized parameters:")
        for param, value in optimized_params.items():
            if isinstance(value, float):
                print(f"   {param}: {value:.3f}")
            else:
                print(f"   {param}: {value}")

        print(f"📊 Best score from optimization: {best_result.get('best_score', 'N/A')}")

        return optimized_params

    except Exception as e:
        print(f"❌ Error loading optimization results: {e}")
        return None

def get_default_parameters():
    """
    Get default parameters for features
    """
    return {
        'sma_1': 50, 'sma_2': 100, 'sma_3': 200,
        'ema_1': 50, 'ema_2': 100,
        'rsi_window': 14, 'macd_signal_span': 9,
        'bb_period': 20, 'bb_std_multiplier': 2.0,
        'aroon_period': 14,
        'n_lag': 5, 'cov_window': 5,
        'short_lookback_1': 5, 'long_lookback_1': 10,
        'Trail_LX': 2, 'Trail_SX': 2
    }