import pandas as pd
import argparse
from tqdm import tqdm
from data import load_and_prepare_data
from model import get_model
from metrics import calculate_performance, plot_results, plot_detailed_analysis, print_summary
from bayesian_optimization import run_bayesian_optimization, load_optimized_parameters, filter_data_features
from optimized_data_preparation import load_and_prepare_data_optimized, compare_parameters

def run_backtest(file_path, minute_data, model_type='nb', train_months=12, test_months=1, features=None):
    """
    Run a backtest using the specified model type
    
    Parameters:
    -----------
    file_path : str
        Path to the data file
    model_type : str
        Type of model to use ('nb', 'rf', 'svm', 'svm_vol', 'logistic', 'nn')
    train_months : int
        Number of months to use for training
    test_months : int
        Number of months to use for testing
    features : list
        List of features to use for training/testing
    """
    # Load and prepare data
    data = load_and_prepare_data(file_path, minute_data, features=None)
    
    # Calculate total months in dataset
    total_months = len(data.resample('ME').count())
    
    # Prepare for walk-forward
    results = []
    all_signals = pd.Series(index=data.index, dtype='float64')
    all_returns = pd.Series(index=data.index, dtype='float64')
    nn_losses = []  # To store neural network losses if applicable
    
    print(f"Running backtest with {model_type.upper()} model...")
    print(f"Using features: {features}")
    
    # Calculate number of iterations
    num_iterations = (total_months - train_months - test_months + 1) // test_months
    
    # Walk-forward loop with progress bar
    for i in tqdm(range(0, total_months - train_months - test_months + 1, test_months), 
                 desc="Backtest Progress", total=num_iterations):
        try:
            # Define train and test periods
            train_end = data.index[0] + pd.DateOffset(months=train_months + i)
            test_end = train_end + pd.DateOffset(months=test_months)
            
            train_data = data[(data.index >= data.index[0] + pd.DateOffset(months=i)) & 
                              (data.index < train_end)]
            test_data = data[(data.index >= train_end) & 
                             (data.index < test_end)]

            if len(train_data) == 0 or len(test_data) == 0:
                continue
            
            # Create and train model (use only data features, not signal generation features)
            data_features = filter_data_features(features)
            model = get_model(model_type, data_features)
            
            # Train model and capture loss if it's a neural network
            if model_type == 'nn':
                mean_loss = model.train(train_data)
                nn_losses.append({
                    'Period': f"{train_data.index[0].date()} to {test_data.index[-1].date()}",
                    'Mean Loss': mean_loss
                })
            else:
                model.train(train_data)
            
            # Generate signals
            test_pred = model.predict(test_data)
            #test_pred.loc[test_pred*test_pred.shift(1) == -1] = 0
            all_signals[test_data.index] = test_pred
            
            # Calculate strategy returns
            test_returns = test_data['Returns'] * test_pred.shift(1).fillna(0)
            all_returns[test_data.index] = test_returns.fillna(0)
            
            # Store period information
            period_results = {
                'Train Start': train_data.index[0].date(),
                'Train End': train_data.index[-1].date(),
                'Test Start': test_data.index[0].date(),
                'Test End': test_data.index[-1].date(),
            }
            
            # Calculate performance metrics
            performance = calculate_performance(test_data, test_pred, test_returns)
            period_results.update(performance)
            
            results.append(period_results)
            
        except Exception as e:
            tqdm.write(f"Error in iteration {i}: {str(e)}")
            continue
    
    # Process results
    if results:
        results_df = pd.DataFrame(results)

        # Add strategy returns and signals to data
        data['Strategy_Returns'] = all_returns
        data['Signal'] = all_signals

        # Calculate cumulative returns (essential for metrics and plotting)
        data['Cumulative_Strategy'] = (data['Strategy_Returns']).cumsum()
        data['Cumulative_Buy_Hold'] = (data['Returns']).cumsum()

        # Plot and print results
        plot_results(data)
        print_summary(results_df, data)

        # Ask user if they want detailed analysis plot
        try:
            show_detailed = input("\nShow detailed analysis plot? (y/n): ").strip().lower()
            if show_detailed == 'y':
                plot_detailed_analysis(data)
        except:
            pass  # Skip if running in non-interactive mode
        
        # Print neural network losses if applicable
        if model_type == 'nn' and nn_losses:
            print("\nNeural Network Training Loss Summary:")
            losses_df = pd.DataFrame(nn_losses)
            print(f"Average Loss: {losses_df['Mean Loss'].mean():.4f}")
            print(f"Min Loss: {losses_df['Mean Loss'].min():.4f}")
            print(f"Max Loss: {losses_df['Mean Loss'].max():.4f}")
        
        return results_df, data
    else:
        print("No valid results were generated. Check your data and parameters.")
        return None, None

def run_backtest_with_optimized_parameters(file_path, minute_data, model_type='svm_vol',
                                         train_months=12, test_months=1, features=None,
                                         optimization_results_file=None):
    """
    Run walk-forward backtest using optimized parameters from Bayesian optimization

    Parameters:
    -----------
    file_path : str
        Path to the data file
    minute_data : int
        Minute data resampling (0 for daily)
    model_type : str
        Type of model to use
    train_months : int
        Number of months for training
    test_months : int
        Number of months for testing
    features : list
        List of features to use
    optimization_results_file : str, optional
        Path to specific optimization results file
    """

    print("🚀 Running backtest with optimized parameters...")

    # Show parameter comparison
    compare_parameters(features, optimization_results_file)

    # Load and prepare data with optimized parameters
    data = load_and_prepare_data_optimized(file_path, minute_data, features, optimization_results_file)

    # Calculate total months in dataset
    total_months = len(data.resample('ME').count())

    # Prepare for walk-forward
    results = []
    all_signals = pd.Series(index=data.index, dtype='float64')
    all_returns = pd.Series(index=data.index, dtype='float64')
    nn_losses = []  # To store neural network losses if applicable

    print(f"\n📊 Running backtest with {model_type.upper()} model...")
    print(f"Using features: {features}")
    print(f"Training: {train_months} months, Testing: {test_months} months")
    print(f"Total data: {total_months} months")

    # Calculate number of iterations
    num_iterations = (total_months - train_months - test_months + 1) // test_months

    # Walk-forward loop with progress bar
    for i in tqdm(range(0, total_months - train_months - test_months + 1, test_months),
                 desc="Optimized Backtest Progress", total=num_iterations):
        try:
            # Define train and test periods
            train_end = data.index[0] + pd.DateOffset(months=train_months + i)
            test_end = train_end + pd.DateOffset(months=test_months)

            train_data = data[(data.index >= data.index[0] + pd.DateOffset(months=i)) &
                              (data.index < train_end)]
            test_data = data[(data.index >= train_end) &
                             (data.index < test_end)]

            if len(train_data) == 0 or len(test_data) == 0:
                continue

            # Create and train model (use only data features, not signal generation features)
            data_features = filter_data_features(features)
            model = get_model(model_type, data_features)

            # Train model and capture loss if it's a neural network
            if model_type == 'nn':
                mean_loss = model.train(train_data)
                nn_losses.append({
                    'Period': f"{train_data.index[0].date()} to {test_data.index[-1].date()}",
                    'Mean Loss': mean_loss
                })
            else:
                model.train(train_data)

            # Generate signals
            test_pred = model.predict(test_data)
            all_signals[test_data.index] = test_pred

            # Calculate strategy returns
            test_returns = test_data['Returns'] * test_pred.shift(1).fillna(0)
            all_returns[test_data.index] = test_returns.fillna(0)

            # Store period information
            period_results = {
                'Train Start': train_data.index[0].date(),
                'Train End': train_data.index[-1].date(),
                'Test Start': test_data.index[0].date(),
                'Test End': test_data.index[-1].date(),
            }

            # Calculate performance metrics
            performance = calculate_performance(test_data, test_pred, test_returns)
            period_results.update(performance)

            results.append(period_results)

        except Exception as e:
            print(f"Error in period {i}: {e}")
            continue

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)

    # Add strategy returns and signals to data for plotting
    data['Strategy_Returns'] = all_returns
    data['Signal'] = all_signals

    # Calculate cumulative returns (essential for metrics and plotting)
    data['Cumulative_Strategy'] = (data['Strategy_Returns']).cumsum()
    data['Cumulative_Buy_Hold'] = (data['Returns']).cumsum()

    if len(results_df) > 0:
        # Plot and print results
        plot_results(data)
        print_summary(results_df, data)

        # Ask user if they want detailed analysis plot
        try:
            show_detailed = input("\nShow detailed analysis plot? (y/n): ").strip().lower()
            if show_detailed == 'y':
                plot_detailed_analysis(data)
        except:
            pass  # Skip if running in non-interactive mode

        # Print neural network losses if applicable
        if model_type == 'nn' and nn_losses:
            print("\nNeural Network Training Loss Summary:")
            losses_df = pd.DataFrame(nn_losses)
            print(f"Average Loss: {losses_df['Mean Loss'].mean():.4f}")
            print(f"Min Loss: {losses_df['Mean Loss'].min():.4f}")
            print(f"Max Loss: {losses_df['Mean Loss'].max():.4f}")

        return results_df, data
    else:
        print("No valid results were generated. Check your data and parameters.")
        return None, None

def parse_arguments():
    parser = argparse.ArgumentParser(description='Run a trading strategy backtest')
    
    parser.add_argument('--file', type=str, required=True,
                        help='Path to the data file')
    
    parser.add_argument('--model', type=str, default='nb', choices=['nb', 'rf', 'svm', 'svm_vol', 'logistic', 'nn'],
                        help='Model type to use for prediction')
    
    parser.add_argument('--train', type=int, default=12,
                        help='Number of months to use for training')
    
    parser.add_argument('--test', type=int, default=1,
                        help='Number of months to use for testing')
    
    parser.add_argument('--features', type=str, nargs='+',
                        default=['SMA_50', 'SMA_100', 'SMA_200'],
                        help='Features to use for training/testing')

    parser.add_argument('--optimize_parameters', action='store_true',
                        help='Run Bayesian optimization for feature hyperparameters')

    parser.add_argument('--n_calls', type=int, default=50,
                        help='Number of Bayesian optimization iterations')

    return parser.parse_args()

if __name__ == "__main__":
    # For command line usage
    try:
        args = parse_arguments()
        file_path = args.file
        model_type = args.model
        train_months = args.train
        test_months = args.test
        features = args.features
        optimize_parameters = args.optimize_parameters
        n_calls = args.n_calls
        minute_data = 0  # Default for command line
    except:
        # Default values for running directly in IDE
        minute_data = 30
        #file_path = 'C:\\Users\\<USER>\\Desktop\\Data1\\Futures\\Daily\\NQ=F.csv'
        file_path = 'C:\\Users\\<USER>\\Desktop\\Data1\\ETF\\QQQ.csv'
        if minute_data != 0:
            file_path = 'C:\\Users\\<USER>\\Desktop\\Data1\\Futures\\1min\\NQ.csv'

        # Choose between Bayesian optimization and single backtest
        optimize_parameters = True  # Set to True to run Bayesian optimization, False for single backtest
        n_calls = 30  # Number of Bayesian optimization iterations

        # Single backtest parameters (used if optimize_parameters=False)
        model_type = 'svm_vol'  # Options: 'nb', 'rf', 'svm', 'svm_vol','logistic', 'nn'
        train_months = 6
        test_months = 2
        features = ['LagReturn1', 'LagReturn2', 'Cov1_1', 'Cov1_2', 'Cov2_2', 'C2C0_LX', 'C2C0_SX']

    if optimize_parameters:
        print("Running Bayesian Optimization...")

        # Run Bayesian optimization for feature hyperparameters
        result = run_bayesian_optimization(
            file_path=file_path,
            minute_data=minute_data,
            model_type=model_type,
            train_months=train_months,
            test_months=test_months,
            features=features,
            n_calls=n_calls
        )

        if result is not None:
            # Print optimization results
            print(f"\n{'='*60}")
            print("BAYESIAN OPTIMIZATION RESULTS")
            print(f"{'='*60}")

            print(f"Best total return: {result['best_score']:.4f}")
            print(f"Optimized parameters:")
            for param, value in result['best_params'].items():
                if isinstance(value, float):
                    print(f"  {param}: {value:.3f}")
                else:
                    print(f"  {param}: {value}")

            print(f"\nRequired parameters based on features: {list(result['required_params'].keys())}")
            print(f"Total optimization iterations: {len(result['optimization_result'].func_vals)}")

            # Save results
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")

            # Save best parameters to CSV
            params_df = pd.DataFrame([result['best_params']])
            params_df['best_score'] = result['best_score']
            params_df['model_type'] = model_type
            params_df['features'] = str(features)

            filename = f'bayesian_optimization_results_{timestamp}.csv'
            params_df.to_csv(filename, index=False)
            print(f"\nBayesian optimization results saved to {filename}")

            run_best = input("\nRun detailed backtest with optimized parameters? (y/n): ").strip().lower()
            if run_best == 'y':
                print("\n🚀 Running detailed backtest with optimized parameters...")
                print("="*60)

                # Run backtest with the optimized parameters
                try:
                    results_df, backtest_data = run_backtest_with_optimized_parameters(
                        file_path=file_path,
                        minute_data=minute_data,
                        model_type=model_type,
                        train_months=train_months,
                        test_months=test_months,
                        features=features,
                        optimization_results_file=filename  # Use the just-saved optimization results
                    )

                    if results_df is not None:
                        print("\n✅ Detailed backtest completed successfully!")
                        print(f"📊 Generated {len(results_df)} test periods")

                        # Show optimization vs backtest comparison
                        print(f"\n📈 PERFORMANCE COMPARISON:")
                        optimization_return = result['best_score']
                        backtest_return = (backtest_data['Cumulative_Strategy'].iloc[-1])
                        print(f"Optimization Score: {optimization_return:.4f}")
                        print(f"Detailed Backtest Return: {backtest_return:.4f}")
                        print(f"Difference: {(backtest_return - optimization_return):.4f}")

                    else:
                        print("❌ Detailed backtest failed to generate results")

                except Exception as e:
                    print(f"❌ Error running detailed backtest: {e}")
                    print("💡 Tip: Check your data file path and feature configuration")
            else:
                print("\n💾 Optimization results saved. You can run the backtest later by setting:")
                print("   optimize_parameters = False")
                print("   The system will automatically load the optimized parameters.")
        else:
            print("Bayesian optimization failed to generate valid results!")

    else:
        # Run backtest with optimized parameters (if available)
        print("Running Backtest with Optimized Parameters...")

        # Check if optimized parameters are available
        optimized_params = load_optimized_parameters(features)

        if optimized_params is not None:
            print("✅ Found optimized parameters from previous Bayesian optimization")
            run_backtest_with_optimized_parameters(
                file_path=file_path,
                minute_data=minute_data,
                model_type=model_type,
                train_months=train_months,
                test_months=test_months,
                features=features
            )
        else:
            print("⚠️  No optimized parameters found, running standard backtest")
            print("   Tip: Run Bayesian optimization first to get optimized parameters")
            run_backtest(
                file_path=file_path,
                minute_data=minute_data,
                model_type=model_type,
                train_months=train_months,
                test_months=test_months,
                features=features
            )


