import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, classification_report

def calculate_performance(test_data, predictions, returns):
    # Calculate accuracy
    accuracy = accuracy_score(test_data['Target'], predictions)

    # Calculate precision for long and short positions
    try:
        class_report = classification_report(test_data['Target'], predictions, output_dict=True, zero_division=1)
        long_precision = class_report.get('1', {}).get('precision', np.nan)
        short_precision = class_report.get('-1', {}).get('precision', np.nan)
    except:
        long_precision = np.nan
        short_precision = np.nan

    # Calculate cumulative return
    cumulative_return = returns.cumsum().iloc[-1]

    # Calculate drawdown in index points
    cum_returns = (returns).cumsum()
    running_max = cum_returns.cummax()
    drawdown_pct = (cum_returns - running_max)
    max_drawdown_pct = drawdown_pct.min()

    # Convert to index points
    initial_value = 1.0  # Starting index value
    max_drawdown_points = max_drawdown_pct - running_max.max()

    # Calculate number of trades and average profit per trade
    predictions_series = pd.Series(predictions, index=test_data.index)

    # Count trades: a trade occurs when position changes from 0 to non-zero or between different non-zero positions
    position_changes = predictions_series != predictions_series.shift(1)
    # Only count as trade if moving to a non-zero position
    trades = position_changes & (predictions_series != 0)
    num_trades = trades.sum()

    # Calculate average profit per trade
    if num_trades > 0:
        # Calculate individual trade returns
        trade_returns = []
        current_position = 0
        trade_start_return = 0

        for i, (idx, position) in enumerate(predictions_series.items()):
            if position != current_position:
                # Position change detected
                if current_position != 0:
                    # End of previous trade - calculate return
                    trade_end_return = returns.cumsum().loc[idx] if idx in returns.cumsum().index else 0
                    trade_profit = trade_end_return - trade_start_return
                    trade_returns.append(trade_profit)

                if position != 0:
                    # Start of new trade
                    trade_start_return = returns.cumsum().loc[idx] if idx in returns.cumsum().index else 0

                current_position = position

        # Handle last trade if it's still open
        if current_position != 0 and len(returns) > 0:
            trade_end_return = returns.cumsum().iloc[-1]
            trade_profit = trade_end_return - trade_start_return
            trade_returns.append(trade_profit)

        avg_profit_per_trade = np.mean(trade_returns) if trade_returns else 0
    else:
        avg_profit_per_trade = 0

    return {
        'Accuracy': accuracy,
        'Long Precision': long_precision,
        'Short Precision': short_precision,
        'Cumulative Return': cumulative_return,
        'Max Drawdown Points': max_drawdown_points,
        'Number of Trades': num_trades,
        'Average Profit per Trade': avg_profit_per_trade,
        'Signal Counts': pd.Series(predictions).value_counts().to_dict()
    }

def plot_results(data):
    """
    Enhanced plotting with equity curves and drawdowns on the same plot
    """
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates

    # Create figure with single subplot for combined equity and drawdown
    fig, ax1 = plt.subplots(1, 1, figsize=(6, 5))

    # Plot equity curves on left axis
    ax1.plot(data.index, data['Cumulative_Strategy'],
             label='Strategy', color='blue', linewidth=2.5)
    ax1.plot(data.index, data['Cumulative_Buy_Hold'],
             label='B&H', color='green', linewidth=2.5)

    ax1.set_xlabel('Date', fontsize=12)
    ax1.set_ylabel('Equity Curve', fontsize=12, color='black')
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='lower left', fontsize=11)

    # Create second y-axis for drawdowns
    ax2 = ax1.twinx()

    # Calculate drawdown percentages
    strategy_peak = data['Cumulative_Strategy'].cummax()
    bh_peak = data['Cumulative_Buy_Hold'].cummax()
    strategy_dd_pct = data['Cumulative_Strategy'] - strategy_peak 
    bh_dd_pct = data['Cumulative_Buy_Hold'] - bh_peak

    # Plot drawdowns on right axis (filled areas)
    ax2.fill_between(data.index, strategy_dd_pct, 0,
                     alpha=0.3, color='red', label='Strategy DD')
    ax2.fill_between(data.index, bh_dd_pct, 0,
                     alpha=0.3, color='orange', label='B&H DD')

    ax2.set_ylabel('Drawdown', fontsize=12, color='red')
    ax2.tick_params(axis='y', labelcolor='red')
    ax2.legend(loc='lower right', fontsize=11)

    # Format x-axis dates
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

    # Set title with performance summary
    strategy_return = data['Cumulative_Strategy'].iloc[-1]
    bh_return = data['Cumulative_Buy_Hold'].iloc[-1]
    strategy_dd = strategy_dd_pct.min()
    bh_dd = bh_dd_pct.min()

    plt.title(f'Strategy: {strategy_return:.1f} return, {strategy_dd:.1f} MDD\n '
              f'B&H: {bh_return:.1f} return, {bh_dd:.1f} MDD',
              fontsize=14, pad=20)

    plt.tight_layout()
    plt.show()

def plot_detailed_analysis(data):
    """
    Create detailed multi-panel analysis plot
    """
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates

    # Create figure with 3 subplots
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(8, 6),
                                        gridspec_kw={'height_ratios': [3, 2, 1]})

    # Panel 1: Equity Curves
    ax1.plot(data.index, data['Cumulative_Strategy'],
             label='Strategy Equity', color='blue', linewidth=2.5)
    ax1.plot(data.index, data['Cumulative_Buy_Hold'],
             label='Buy & Hold Equity', color='green', linewidth=2.5)
    ax1.set_ylabel('Equity Curve', fontsize=12)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_title('Equity Curves Comparison', fontsize=14)

    # Panel 2: Drawdowns
    strategy_peak = data['Cumulative_Strategy'].cummax()
    bh_peak = data['Cumulative_Buy_Hold'].cummax()
    strategy_dd_pct = (data['Cumulative_Strategy'] - strategy_peak)
    bh_dd_pct = (data['Cumulative_Buy_Hold'] - bh_peak) 

    ax2.fill_between(data.index, strategy_dd_pct, 0,
                     alpha=0.6, color='red', label='Strategy Drawdown')
    ax2.fill_between(data.index, bh_dd_pct, 0,
                     alpha=0.6, color='orange', label='Buy & Hold Drawdown')
    ax2.set_ylabel('Drawdown', fontsize=12)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_title('Drawdown Analysis', fontsize=14)

    # Panel 3: Trading Signals
    if 'Signal' in data.columns:
        signal_colors = {-1: 'red', 0: 'gray', 1: 'green'}
        for signal in [-1, 0, 1]:
            mask = data['Signal'] == signal
            if mask.any():
                ax3.scatter(data.index[mask], data['Signal'][mask],
                           c=signal_colors[signal], alpha=0.6, s=20,
                           label=f'Signal {signal}')

        ax3.set_ylabel('Signal', fontsize=12)
        ax3.legend(fontsize=11)
        ax3.set_title('Trading Signals', fontsize=14)
    else:
        ax3.text(0.5, 0.5, 'No Signal Data Available',
                transform=ax3.transAxes, ha='center', va='center', fontsize=12)
        ax3.set_title('Trading Signals', fontsize=14)

    ax3.set_xlabel('Date', fontsize=12)
    ax3.grid(True, alpha=0.3)

    # Format x-axis dates for all subplots
    for ax in [ax1, ax2, ax3]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=12))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    plt.show()
    
def print_summary(results_df, data):
    # Calculate strategy drawdown in index points
    strategy_peak = data['Cumulative_Strategy'].cummax()
    strategy_dd_points = data['Cumulative_Strategy'] - strategy_peak
    max_strategy_dd = strategy_dd_points.min()
    
    # Calculate buy-hold drawdown in index points
    bh_peak = data['Cumulative_Buy_Hold'].cummax()
    bh_dd_points = data['Cumulative_Buy_Hold'] - bh_peak
    max_bh_dd = bh_dd_points.min()
    
    # Calculate final equity values and recovery factors
    strategy_final_equity = data['Cumulative_Strategy'].iloc[-1]
    bh_final_equity = data['Cumulative_Buy_Hold'].iloc[-1]

    strategy_recovery_factor = strategy_final_equity / abs(max_strategy_dd) if max_strategy_dd != 0 else float('inf')
    bh_recovery_factor = bh_final_equity / abs(max_bh_dd) if max_bh_dd != 0 else float('inf')

    # Calculate percentage drawdowns for display
    strategy_dd_pct = (max_strategy_dd) if strategy_peak.max() != 0 else 0
    bh_dd_pct = (max_bh_dd ) if bh_peak.max() != 0 else 0

    # Print enhanced performance summary
    print("\n" + "="*80)
    print("COMPREHENSIVE PERFORMANCE SUMMARY")
    print("="*80)

    print(f"\n📊 ACCURACY METRICS:")
    print(f"Average Accuracy: {results_df['Accuracy'].mean():.2%}")
    print(f"Average Long Precision: {results_df['Long Precision'].mean():.2%}")
    print(f"Average Short Precision: {results_df['Short Precision'].mean():.2%}")

    print(f"\n💰 FINAL EQUITY:")
    print(f"Strategy Final Equity: {strategy_final_equity:.4f}")
    print(f"Buy-Hold Final Equity: {bh_final_equity:.4f}")
    print(f"Strategy vs Buy-Hold: {((strategy_final_equity / bh_final_equity) - 1) * 100:+.2f}%")

    print(f"\n📈 TOTAL RETURNS:")
    print(f"Total Strategy Return: {strategy_final_equity:.2f}")
    print(f"Total Buy-Hold Return: {bh_final_equity :.2f}")
    print(f"Excess Return: {strategy_final_equity - bh_final_equity:.2f}")

    print(f"\n📉 MAXIMUM DRAWDOWN:")
    print(f"Strategy Max Drawdown: {strategy_dd_pct:.2f}")
    print(f"Buy-Hold Max Drawdown: {bh_dd_pct:.2f}")

    print(f"\n🔄 RECOVERY FACTOR:")
    print(f"Strategy Recovery Factor: {strategy_recovery_factor:.2f}")
    print(f"Buy-Hold Recovery Factor: {bh_recovery_factor:.2f}")

    print(f"\n📊 TRADE STATISTICS:")
    total_trades = results_df['Number of Trades'].sum()
    avg_profit_per_trade = results_df['Average Profit per Trade'].mean()
    print(f"Total Number of Trades: {total_trades}")
    print(f"Average Profit per Trade: {avg_profit_per_trade:.4f}")

    # Show signal distribution
    print("\nSignal Distribution Across All Tests:")
    signal_counts = pd.DataFrame(results_df['Signal Counts'].tolist()).sum()
    print(signal_counts.astype(int))

    # Calculate drawdown percentages for plotting
    data['Strategy_Drawdown_Pct'] = (data['Cumulative_Strategy'] / strategy_peak - 1) * 100
    data['BuyHold_Drawdown_Pct'] = (data['Cumulative_Buy_Hold'] / bh_peak - 1) * 100




