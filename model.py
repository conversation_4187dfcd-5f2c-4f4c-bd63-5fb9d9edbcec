from sklearn.naive_bayes import GaussianNB
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader

def volatility_adaptive_rbf(X, Y=None, gamma=1.0):
    """ Custom RBF kernel with volatility-adjusted bandwidth """
    if Y is None:
        Y = X
    # Compute local volatility (e.g., rolling std)
    sigma_X = np.std(X, axis=1, keepdims=True)
    sigma_Y = np.std(Y, axis=1, keepdims=True)
    # Pairwise volatility-adjusted distances
    D = np.sum(X**2, axis=1)[:, np.newaxis] + np.sum(Y**2, axis=1) - 2 * X @ Y.T
    K = np.exp(-gamma * D / (sigma_X + sigma_Y.T + 1e-8))
    return K

class NBModel:
    def __init__(self, features=None):
        self.model = GaussianNB()
        self.scaler = StandardScaler()
        self.features = features
    
    def train(self, train_data):
        X_train = train_data[self.features]
        y_train = train_data['Target']
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
    
    def predict(self, test_data):
        X_test = test_data[self.features]
        X_test_scaled = self.scaler.transform(X_test)
        
        # Generate predictions
        return pd.Series(self.model.predict(X_test_scaled), index=test_data.index)

class RFModel:
    def __init__(self, features=None):
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.features = features
    
    def train(self, train_data):
        X_train = train_data[self.features]
        y_train = train_data['Target']
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
    
    def predict(self, test_data):
        X_test = test_data[self.features]
        X_test_scaled = self.scaler.transform(X_test)
        
        # Generate predictions
        return pd.Series(self.model.predict(X_test_scaled), index=test_data.index)

class SVMModel:
    def __init__(self, features=None):
        self.model = SVC(kernel='rbf', probability=True)
        self.scaler = StandardScaler()
        self.features = features

    def train(self, train_data):
        X_train = train_data[self.features]
        y_train = train_data['Target']

        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)

        # Train model
        self.model.fit(X_train_scaled, y_train)

    def predict(self, test_data):
        X_test = test_data[self.features]
        X_test_scaled = self.scaler.transform(X_test)

        # Generate predictions
        return pd.Series(self.model.predict(X_test_scaled), index=test_data.index)

class VolatilityAdaptiveSVMModel:
    def __init__(self, features=None, gamma=1.0):
        self.scaler = StandardScaler()
        self.features = features
        self.gamma = gamma
        self.model = None
        self.X_train_scaled = None  # Store training data for kernel computation

    def train(self, train_data):
        X_train = train_data[self.features]
        y_train = train_data['Target']

        # Scale features
        self.X_train_scaled = self.scaler.fit_transform(X_train)

        # Create Gram matrix using custom kernel
        gram_matrix = volatility_adaptive_rbf(self.X_train_scaled, gamma=self.gamma)

        # Train SVM with precomputed kernel
        self.model = SVC(kernel='precomputed', probability=True)
        self.model.fit(gram_matrix, y_train)

    def predict(self, test_data):
        X_test = test_data[self.features]
        X_test_scaled = self.scaler.transform(X_test)

        # Compute kernel matrix between test and training data
        kernel_matrix = volatility_adaptive_rbf(X_test_scaled, self.X_train_scaled, gamma=self.gamma)

        # Generate predictions
        predictions = self.model.predict(kernel_matrix)
        return pd.Series(predictions, index=test_data.index)

class LogisticModel:
    def __init__(self, features=None):
        self.model = LogisticRegression(max_iter=1000, random_state=42)
        self.scaler = StandardScaler()
        self.features = features
    
    def train(self, train_data):
        X_train = train_data[self.features]
        y_train = train_data['Target']
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
    
    def predict(self, test_data):
        X_test = test_data[self.features]
        X_test_scaled = self.scaler.transform(X_test)
        
        # Generate predictions
        return pd.Series(self.model.predict(X_test_scaled), index=test_data.index)

class NeuralNetModel(nn.Module):
    def __init__(self, input_size, hidden_size=64, output_size=3):
        super(NeuralNetModel, self).__init__()
        self.layer1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.layer2 = nn.Linear(hidden_size, hidden_size//2)
        self.layer3 = nn.Linear(hidden_size//2, output_size)
        self.softmax = nn.Softmax(dim=1)
        
    def forward(self, x):
        x = self.layer1(x)
        x = self.relu(x)
        x = self.layer2(x)
        #x = self.relu(x)
        #x = self.layer3(x)
        x = self.softmax(x)
        return x

class PyTorchModel:
    def __init__(self, features=None):
        self.features = features
        self.scaler = StandardScaler()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.classes = None
        
    def train(self, train_data, epochs=50, batch_size=32):
        X_train = train_data[self.features].values
        y_train = train_data['Target'].values
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Get unique classes and map them to indices
        self.classes = np.unique(y_train)
        class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        y_indices = np.array([class_to_idx[y] for y in y_train])
        
        # Convert to PyTorch tensors
        X_tensor = torch.FloatTensor(X_train_scaled)
        y_tensor = torch.LongTensor(y_indices)
        
        # Create dataset and dataloader
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # Initialize model
        self.model = NeuralNetModel(len(self.features), output_size=len(self.classes))
        self.model.to(self.device)
        
        # Define loss function and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=0.001)
        
        # Training loop
        self.model.train()
        total_loss = 0.0
        total_batches = 0
        
        for epoch in range(epochs):
            epoch_loss = 0.0
            batch_count = 0
            
            for inputs, labels in dataloader:
                inputs, labels = inputs.to(self.device), labels.to(self.device)
                
                # Zero the parameter gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.model(inputs)
                loss = criterion(outputs, labels)
                
                # Backward pass and optimize
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            total_loss += epoch_loss
            total_batches += batch_count
        
        # Calculate mean loss but don't print it
        mean_loss = total_loss / (total_batches * epochs)
        return mean_loss
    
    def predict(self, test_data):
        X_test = test_data[self.features].values
        X_test_scaled = self.scaler.transform(X_test)
        
        # Convert to PyTorch tensor
        X_tensor = torch.FloatTensor(X_test_scaled).to(self.device)
        
        # Set model to evaluation mode
        self.model.eval()
        
        # Generate predictions
        with torch.no_grad():
            outputs = self.model(X_tensor)
            _, predicted = torch.max(outputs, 1)
            predicted = predicted.cpu().numpy()
        
        # Map indices back to original classes
        predictions = [self.classes[idx] for idx in predicted]
        
        return pd.Series(predictions, index=test_data.index)

def get_model(model_type, features=None):
    """Factory function to create model instances"""
    models = {
        'nb': NBModel,
        'rf': RFModel,
        'svm': SVMModel,
        'svm_vol': VolatilityAdaptiveSVMModel,
        'logistic': LogisticModel,
        'nn': PyTorchModel
    }

    if model_type not in models:
        raise ValueError(f"Model type '{model_type}' not supported. Choose from: {list(models.keys())}")

    return models[model_type](features)




