"""
Data preparation module that uses optimized parameters from Bayesian optimization
"""

import pandas as pd
from bayesian_optimization import load_optimized_parameters, get_default_parameters, calculate_features_with_params

def load_and_prepare_data_optimized(file_path, minute_data=0, features=None, optimization_results_file=None):
    """
    Load and prepare data using optimized parameters from Bayesian optimization results
    
    Parameters:
    -----------
    file_path : str
        Path to the data file
    minute_data : int
        Minute data resampling (0 for daily)
    features : list
        List of features to use
    optimization_results_file : str, optional
        Path to specific optimization results file
    
    Returns:
    --------
    pandas.DataFrame : Prepared data with optimized features
    """
    
    print("🔧 Preparing data with optimized parameters...")
    
    # Load base data
    base_data = pd.read_csv(file_path, parse_dates=True, index_col='Date')
    if minute_data != 0:
        base_data = base_data.resample(str(minute_data)+'min').agg({
            'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'
        })
        base_data.dropna(inplace=True)
    base_data.sort_index(inplace=True)
    base_data['Returns'] = base_data['Open'].diff()
    
    # Try to load optimized parameters
    optimized_params = load_optimized_parameters(features, optimization_results_file)
    
    if optimized_params is None:
        print("⚠️  No optimized parameters found, using default parameters")
        optimized_params = get_default_parameters()
        print("📋 Using default parameters:")
        for param, value in optimized_params.items():
            if isinstance(value, float):
                print(f"   {param}: {value:.3f}")
            else:
                print(f"   {param}: {value}")
    
    # Calculate features with optimized parameters
    print("🧮 Calculating features with optimized parameters...")
    data = calculate_features_with_params(base_data, optimized_params, features, minute_data)
    
    print(f"✅ Data preparation completed!")
    print(f"   Data shape: {data.shape}")
    print(f"   Date range: {data.index[0].strftime('%Y-%m-%d')} to {data.index[-1].strftime('%Y-%m-%d')}")
    print(f"   Features used: {features}")
    
    return data

def compare_parameters(features, optimization_results_file=None):
    """
    Compare default parameters with optimized parameters
    
    Parameters:
    -----------
    features : list
        List of features used
    optimization_results_file : str, optional
        Path to optimization results file
    """
    print("\n" + "="*60)
    print("PARAMETER COMPARISON")
    print("="*60)
    
    # Get default parameters
    default_params = get_default_parameters()
    
    # Try to load optimized parameters
    optimized_params = load_optimized_parameters(features, optimization_results_file)
    
    if optimized_params is None:
        print("❌ No optimized parameters available for comparison")
        return
    
    # Compare parameters
    print(f"\n{'Parameter':<20} {'Default':<12} {'Optimized':<12} {'Change':<10}")
    print("-" * 60)
    
    all_params = set(default_params.keys()) | set(optimized_params.keys())
    
    for param in sorted(all_params):
        default_val = default_params.get(param, 'N/A')
        optimized_val = optimized_params.get(param, 'N/A')
        
        if default_val != 'N/A' and optimized_val != 'N/A':
            if isinstance(default_val, (int, float)) and isinstance(optimized_val, (int, float)):
                change = ((optimized_val - default_val) / default_val) * 100
                change_str = f"{change:+.1f}%"
            else:
                change_str = "N/A"
        else:
            change_str = "N/A"
        
        # Format values
        if isinstance(default_val, float):
            default_str = f"{default_val:.3f}"
        else:
            default_str = str(default_val)
            
        if isinstance(optimized_val, float):
            optimized_str = f"{optimized_val:.3f}"
        else:
            optimized_str = str(optimized_val)
        
        print(f"{param:<20} {default_str:<12} {optimized_str:<12} {change_str:<10}")

def test_optimized_data_preparation():
    """
    Test the optimized data preparation functionality
    """
    print("Testing Optimized Data Preparation")
    print("="*40)
    
    # Test configuration
    file_path = 'C:\\Users\\<USER>\\Desktop\\Data1\\ETF\\^GDAXI.csv'
    features = ['LagReturn1', 'LagReturn2', 'Cov1_1', 'Cov1_2', 'Cov2_2']
    
    try:
        # Test loading and preparing data
        data = load_and_prepare_data_optimized(
            file_path=file_path,
            minute_data=0,
            features=features
        )
        
        print(f"\n✅ Data preparation successful!")
        print(f"   Shape: {data.shape}")
        print(f"   Columns: {list(data.columns)}")
        
        # Test parameter comparison
        compare_parameters(features)
        
    except FileNotFoundError:
        print(f"❌ Data file not found: {file_path}")
        print("   Please check the file path")
    except Exception as e:
        print(f"❌ Error in data preparation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_data_preparation()
