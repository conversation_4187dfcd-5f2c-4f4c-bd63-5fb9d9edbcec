import pandas as pd
import numpy as np

def load_and_prepare_data(file_path, minute_data=0, features=None):
    # Load data
    data = pd.read_csv(file_path, parse_dates=True, index_col='Date')
    if minute_data != 0:
        data = data.resample(str(minute_data)+'min').agg({'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'})
        #data = data[data.index.time >= pd.to_datetime('15:00').time()]
        data.dropna(inplace=True)
    data.sort_index(inplace=True)
    
    # Create returns
    #data['Returns'] = data['Close']/data['Open']-1
    #data['Returns'] = data['Open'].pct_change()
    data['Returns'] = data['Open'].diff()
    # Calculate technical indicators
    data['SMA_1'] = data['Close'].rolling(50).mean()/data['Close']
    data['SMA_2'] = data['Close'].rolling(100).mean()/data['Close']
    data['SMA_3'] = data['Close'].rolling(200).mean()/data['Close']
    data['SMA_1_2'] = data['SMA_1']/data['SMA_2']
    data['SMA_1_3'] = data['SMA_1']/data['SMA_3']
    data['SMA_2_3'] = data['SMA_2']/data['SMA_3']
    
    # Add RSI
    # NQ
    rsi_window = 7
    delta = data['Close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=rsi_window).mean()
    avg_loss = loss.rolling(window=rsi_window).mean()
    rs = avg_gain / avg_loss
    data['RSI'] = 1 - (1 / (1 + rs))
    
    # QQQ
    data['EMA_1'] = data['Close'].ewm(span=50, adjust=False).mean()
    data['EMA_2'] = data['Close'].ewm(span=100, adjust=False).mean()
    if minute_data != 0:
        # NQ
        data['EMA_1'] = data['Close'].ewm(span=2, adjust=False).mean()
        data['EMA_2'] = data['Close'].ewm(span=4, adjust=False).mean()
    # Add MACD
    data['MACD'] = data['EMA_1'] - data['EMA_2']
    data['MACD_Signal'] = data['MACD'].ewm(span=9, adjust=False).mean()
    data['MACD_Hist'] = data['MACD'] - data['MACD_Signal']
    
    # Add Bollinger Bands
    data['BB_Middle'] = data['Close'].rolling(20).mean()
    data['BB_Std'] = data['Close'].rolling(20).std()
    data['BB_Upper'] = data['BB_Middle'] + (data['BB_Std'] * 2)
    data['BB_Lower'] = data['BB_Middle'] - (data['BB_Std'] * 2)
    if minute_data != 0:
        data['BB_Middle'] = data['Close'].rolling(20).mean()
        data['BB_Std'] = data['Close'].rolling(20).std()
        data['BB_Upper'] = data['BB_Middle'] + (data['BB_Std'] * 3)
        data['BB_Lower'] = data['BB_Middle'] - (data['BB_Std'] * 3)
    data['BB_Width'] = (data['BB_Upper'] - data['BB_Lower']) / data['BB_Middle']
    
    # Add lagged returns
    n_lag = 5
    for lag in range(1, n_lag+1):
        data['LagReturn' + str(lag)] = data['Close'].pct_change(lag)
    # Add covariance of lagged returns
    for lag1 in range(1, n_lag+1):
        for lag2 in range(lag1, n_lag+1):
            data['Cov' + str(lag1) + '_' + str(lag2)] = data['LagReturn' + str(lag1)].rolling(5).cov(data['LagReturn' + str(lag2)])
    
    # Create signals
    data['Signal'] = 0  # Initialize with flat position (0)
    
    # QQQ cov, KS200 cov
    '''
    data.loc[data['Close'].shift(-15) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-20) > data['Close'], 'Signal'] = 1  # Long position (1)
    '''
    '''
    # HSI cov
    data.loc[data['Close'].shift(-15) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-20) > data['Close'], 'Signal'] = 1  # Long position (1)
    data.loc[data['Close'].shift(-5) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-40) > data['Close'], 'Signal'] = 1  # Long position (1)
    '''
    '''
    # N225 cov
    data.loc[data['Close'].shift(-5) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-25) > data['Close'], 'Signal'] = 1  # Long position (1)
    data.loc[data['Close'].shift(-10) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-35) > data['Close'], 'Signal'] = 1  # Long position (1)
    '''
    # GDAXI cov
    data.loc[data['Close'].shift(-5) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-10) > data['Close'], 'Signal'] = 1  # Long position (1)
    data.loc[data['Close'].shift(-20) < data['Close'], 'Signal'] = -1  # Short position (-1)
    data.loc[data['Close'].shift(-40) > data['Close'], 'Signal'] = 1  # Long position (1)
    if minute_data != 0:
        # NQ
        data.loc[data['Close'].shift(-2) < data['Close'], 'Signal'] = -1  # Short position (-1)
        data.loc[data['Close'].shift(-5) > data['Close'], 'Signal'] = 1  # Long position (1)
        data.loc[data.index.time > pd.to_datetime('15:30').time(), 'Signal'] = 0
    # Create target by shifting signals forward
    data['Target'] = data['Signal'].shift(-1)
    
    data.dropna(inplace=True)

    return data
